const JSON_FIELDS = ['preQualifyFields', 'applicationFields', 'pandadoc', 'utm', 'meta', 'agent'];

const ALLOWED_FIELDS = [
  'uuid',
  'version',
  'status',
  'domain',
  'preQualifyFields',
  'approvalAmount',
  'agent',
  'reason',
  'applicationFields',
  'pandadoc',
  'utm',
  'meta',
  'fastTrack',
  'salesforce_id',
  'bank_stmts',
  'created_at',
  'updated_at',
  'started_at',
  'submitted_at',
  'signed_at',
  'completed_at',
  'edited_at',
];

// New ApplicationD1 class
export class ApplicationD1 {
  static async create(env, data) {
    if (!data || !data.uuid) {
      throw new Error('Missing UUID for D1 create');
    }

    const fields = Object.keys(data).filter((field) => ALLOWED_FIELDS.includes(field));
    const values = fields.map((field) => {
      const value = data[field];
      if (JSON_FIELDS.includes(field)) {
        return value ? JSON.stringify(value) : null;
      }
      if (field === 'fastTrack') {
        return value === true ? 1 : 0;
      }
      return value;
    });

    const statement = env.DB.prepare(`INSERT INTO applications (${fields.join(', ')}) VALUES (${fields.map(() => '?').join(', ')})`).bind(
      ...values
    );

    await statement.run();
    return data;
  }

  static async update(env, uuid, fields) {
    if (!uuid) throw new Error('Missing UUID for D1 update');
    if (!fields || Object.keys(fields).length === 0) {
      throw new Error('Missing fields for D1 update');
    }

    const updatedFields = Object.keys(fields).reduce((acc, field) => {
      if (ALLOWED_FIELDS.includes(field)) {
        acc[field] = fields[field];
      }
      return acc;
    }, {});

    if (!updatedFields.updated_at) {
      updatedFields.updated_at = new Date().toISOString();
    }

    const fieldNames = Object.keys(updatedFields);
    const setClause = fieldNames.map((field) => `${field} = ?`).join(', ');
    const values = fieldNames.map((field) => {
      const value = updatedFields[field];
      if (JSON_FIELDS.includes(field)) {
        return value ? JSON.stringify(value) : null;
      }
      if (field === 'fastTrack') {
        return value === true ? 1 : 0;
      }
      return value;
    });

    const statement = env.DB.prepare(`UPDATE applications SET ${setClause} WHERE uuid = ?`).bind(...values, uuid);

    await statement.run();
    return updatedFields;
  }

  static async get(env, uuid) {
    if (!uuid) return null;

    const statement = env.DB.prepare(
      `SELECT * FROM applications
       WHERE uuid = ?`
    ).bind(uuid);

    const result = await statement.first();
    if (!result) return null;

    return this.#transformRawApplication(result);
  }

  static async getAll(env) {
    const statement = env.DB.prepare(`SELECT * FROM applications`);

    const result = await statement.all();
    return result.results.map(this.#transformRawApplication);
  }

  static #parseJsonField(value) {
    if (value === null || value === undefined || value === 'NULL') {
      return null;
    }
    try {
      return JSON.parse(value);
    } catch (error) {
      // console.error('Error parsing JSON field:', error);
      return value;
    }
  }

  static #transformRawApplication(rawApp) {
    if (!rawApp) return null;

    const app = { ...rawApp };

    for (const field of JSON_FIELDS) {
      if (typeof app[field] === 'string') {
        app[field] = this.#parseJsonField(app[field]);
      }
    }

    app.fastTrack = app.fastTrack === 1;

    return app;
  }
}
