import { createFactory } from 'hono/factory';
import { RoundRobin } from '../round-robin';

const factory = createFactory();

export const roundRobinAgentsHandlers = factory.createHandlers(async (c) => {
  const agents = await RoundRobin.getAgents(c.env);
  const index = await RoundRobin.getIndex(c.env);
  return c.json({ agents, index });
});

export const nextAgentHandlers = factory.createHandlers(async (c) => {
  const agents = await RoundRobin.getAgents(c.env);
  const index = { before: await RoundRobin.getIndex(c.env) };
  const assigned = await RoundRobin.next(c.env);
  index.after = await RoundRobin.getIndex(c.env);
  return c.json({ agents: agents.map((o) => o.name), assigned: assigned.name, index });
});

export const previousAgentHandlers = factory.createHandlers(async (c) => {
  const agents = await RoundRobin.getAgents(c.env);
  const index = { before: await RoundRobin.getIndex(c.env) };
  const assigned = await RoundRobin.previous(c.env);
  index.after = await RoundRobin.getIndex(c.env);
  return c.json({ agents: agents.map((o) => o.name), assigned: assigned.name, index });
});
